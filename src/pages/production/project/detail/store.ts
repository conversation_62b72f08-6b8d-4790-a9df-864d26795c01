import { create } from 'zustand'
import { post, get as requestGet } from '../../../../utils/request'
import type { BudgetCategory, BudgetSubcategory } from '../../../../consts/budget'

// 预算类目信息（对应API的PrProductionBudgetItems）
export interface IPrProductionBudgetItem {
  id?: number // 主键ID
  productionId: number // 关联项目ID
  isPackage?: boolean // 是否打包
  itemName?: string // 打包名称
  quotedPrice?: number // 单价
  personCount: number // 人数
  dayCount: number // 拍摄天数
  totalPrice?: number // 总金额
  hasInvoice?: number // 是否正规发票（1=是，0=否）
  description?: string // 备注说明
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  itemsDetail?: IPrProductionItemsDetail[] // 打包子类列表
}

// 打包子类信息（对应API的PrProductionItemsDetail）
export interface IPrProductionItemsDetail {
  id?: number // 主键ID
  itemId: number // 打包项Id
  category: BudgetCategory // 一级分类
  subcategory: BudgetSubcategory // 二级分类
  personCount: number // 人数
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  categoryLabel?: string // 一级分类标签（只读）
  subcategoryLabel?: string // 二级分类标签（只读）
}

// 获取预算项目参数（对应API的GetBudgetItems）
export interface IGetBudgetItemsParams {
  productionId: number // 项目ID
}

// 保存预算类目参数（对应API的SaveProductionBudgetItemsDto）
export interface ISaveProductionBudgetItemsDto {
  productionId: number // 项目ID
  budgetItems?: IPrProductionBudgetItem[] // 预算类目列表
}

// API响应基础结构
export interface IWebResponseContent<T = any> {
  status: boolean
  code?: string
  message?: string
  data?: T
}

export interface IProjectDetailStore {
  /* API调用方法 */

  // 获取预算项目列表
  getBudgetItems: (params: IGetBudgetItemsParams) => Promise<IPrProductionBudgetItem[] | null>

  // 根据ID获取预算项目详情
  getBudgetItemsById: (id: number) => Promise<IPrProductionBudgetItem | null>

  // 保存预算项目信息
  saveBudgetItems: (params: ISaveProductionBudgetItemsDto) => Promise<boolean>

  // 删除预算项目
  deleteBudgetItems: (id: number) => Promise<boolean>
}

export default create<IProjectDetailStore>(() => ({
  // 获取预算项目列表
  getBudgetItems: async (params: IGetBudgetItemsParams) => {
    try {
      const { data, status } = await post<IWebResponseContent<IPrProductionBudgetItem[]>, any>(
        '/ProductionFinance/GetBudgetItems',
        null,
        { params }
      )

      if (status && data?.data) {
        return Array.isArray(data.data) ? data.data : []
      }

      return null
    } catch (error) {
      console.error('获取预算项目列表失败:', error)

      return null
    }
  },

  // 根据ID获取预算项目详情
  getBudgetItemsById: async (id: number) => {
    try {
      const { data, status } = await requestGet<IWebResponseContent<IPrProductionBudgetItem>, any>(
        `/ProductionFinance/GetBudgetItemsById?id=${id}`
      )

      if (status && data?.data) {
        return data.data
      }

      return null
    } catch (error) {
      console.error('获取预算项目详情失败:', error)

      return null
    }
  },

  // 保存预算项目信息
  saveBudgetItems: async (params: ISaveProductionBudgetItemsDto) => {
    try {
      const { data, status } = await post<IWebResponseContent, any>('/ProductionFinance/Save', params)

      return !!(status && data?.status)
    } catch (error) {
      console.error('保存预算项目信息失败:', error)

      return false
    }
  },

  // 删除预算项目
  deleteBudgetItems: async (id: number) => {
    try {
      const { data, status } = await requestGet<IWebResponseContent, any>(
        `/ProductionFinance/DeleteBudgetItems?id=${id}`
      )

      return !!(status && data?.status)
    } catch (error) {
      console.error('删除预算项目失败:', error)

      return false
    }
  },
}))