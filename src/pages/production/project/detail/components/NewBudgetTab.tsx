import { DeleteOutlined, EditOutlined, PlusOutlined, SaveOutlined } from '@ant-design/icons'
import { Button, Card, Flex, Form, Input, InputNumber, message, Popconfirm, Select, Space, Table, Typography } from 'antd'
import React, { useCallback, useEffect, useState } from 'react'
import {
  BUDGET_CATEGORY_CONFIG,
  BUDGET_CATEGORY_OPTIONS,
  BUDGET_SUBCATEGORY_CONFIG,
  getBudgetCategoryLabel,
  getBudgetSubcategoryLabel,
  getSubcategoryOptionsByCategory,
  HAS_INVOICE_OPTIONS,
  IS_PACKAGE_OPTIONS,
  type BudgetCategory,
  type BudgetSubcategory,
} from '../../../../../consts/budget'
import { IProductionListItem } from '../../list/store'
import useProjectDetailStore, { IPrProductionBudgetItem, IPrProductionItemsDetail } from '../store'

const { Title, Text } = Typography

interface INewBudgetTabProps {
  productionId: number
  project?: IProductionListItem
}

interface IBudgetItemWithKey extends IPrProductionBudgetItem {
  key: string
  editing?: boolean
}

const NewBudgetTab: React.FC<INewBudgetTabProps> = ({ productionId, project }) => {
  const [budgetItems, setBudgetItems] = useState<IBudgetItemWithKey[]>([])
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [form] = Form.useForm()

  const { getBudgetItems, saveBudgetItems, deleteBudgetItems } = useProjectDetailStore()

  // 加载预算数据
  const loadBudgetData = useCallback(async () => {
    if (!productionId) return

    setLoading(true)
    try {
      const data = await getBudgetItems({ productionId })
      if (data) {
        const itemsWithKey = data.map((item, index) => ({
          ...item,
          key: item.id?.toString() || `new-${index}`,
        }))
        setBudgetItems(itemsWithKey)
      }
    } catch (error) {
      console.error('加载预算数据失败:', error)
      message.error('加载预算数据失败')
    } finally {
      setLoading(false)
    }
  }, [productionId, getBudgetItems])

  useEffect(() => {
    loadBudgetData()
  }, [loadBudgetData])

  // 添加新行
  const handleAddRow = () => {
    const newItem: IBudgetItemWithKey = {
      key: `new-${Date.now()}`,
      productionId,
      personCount: 1,
      dayCount: 1,
      editing: true,
      itemsDetail: [],
    }
    setBudgetItems([...budgetItems, newItem])
  }

  // 编辑行
  const handleEditRow = (key: string) => {
    setBudgetItems(items =>
      items.map(item => (item.key === key ? { ...item, editing: true } : { ...item, editing: false }))
    )
  }

  // 保存行
  const handleSaveRow = async (key: string) => {
    try {
      const values = await form.validateFields()
      const itemIndex = budgetItems.findIndex(item => item.key === key)
      if (itemIndex === -1) return

      const updatedItem = { ...budgetItems[itemIndex], ...values, editing: false }
      
      // 计算总价
      if (updatedItem.quotedPrice && updatedItem.personCount && updatedItem.dayCount) {
        updatedItem.totalPrice = updatedItem.quotedPrice * updatedItem.personCount * updatedItem.dayCount
      }

      const newItems = [...budgetItems]
      newItems[itemIndex] = updatedItem
      setBudgetItems(newItems)

      message.success('保存成功')
    } catch (error) {
      console.error('保存失败:', error)
    }
  }

  // 删除行
  const handleDeleteRow = async (key: string) => {
    const item = budgetItems.find(item => item.key === key)
    if (!item) return

    if (item.id) {
      // 如果有ID，调用删除接口
      try {
        const success = await deleteBudgetItems(item.id)
        if (success) {
          setBudgetItems(items => items.filter(item => item.key !== key))
          message.success('删除成功')
        } else {
          message.error('删除失败')
        }
      } catch (error) {
        console.error('删除失败:', error)
        message.error('删除失败')
      }
    } else {
      // 新增的行直接从列表中移除
      setBudgetItems(items => items.filter(item => item.key !== key))
    }
  }

  // 批量保存
  const handleSaveAll = async () => {
    setSaving(true)
    try {
      const itemsToSave = budgetItems
        .filter(item => !item.editing)
        .map(item => {
          const { key, editing, ...rest } = item
          return rest
        })

      const success = await saveBudgetItems({
        productionId,
        budgetItems: itemsToSave,
      })

      if (success) {
        message.success('保存成功')
        await loadBudgetData() // 重新加载数据
      } else {
        message.error('保存失败')
      }
    } catch (error) {
      console.error('保存失败:', error)
      message.error('保存失败')
    } finally {
      setSaving(false)
    }
  }

  // 取消编辑
  const handleCancelEdit = (key: string) => {
    const item = budgetItems.find(item => item.key === key)
    if (!item) return

    if (!item.id) {
      // 新增的行直接删除
      setBudgetItems(items => items.filter(item => item.key !== key))
    } else {
      // 已存在的行恢复原状态
      setBudgetItems(items => items.map(item => (item.key === key ? { ...item, editing: false } : item)))
    }
  }

  const columns = [
    {
      title: '项目类别',
      dataIndex: 'itemName',
      key: 'itemName',
      width: 200,
      render: (text: string, record: IBudgetItemWithKey) => {
        if (record.editing) {
          return (
            <Form.Item name="itemName" className="no-margin" rules={[{ required: true, message: '请输入项目名称' }]}>
              <Input placeholder="请输入项目名称" />
            </Form.Item>
          )
        }
        return text || '-'
      },
    },
    {
      title: '人数',
      dataIndex: 'personCount',
      key: 'personCount',
      width: 100,
      align: 'center' as const,
      render: (text: number, record: IBudgetItemWithKey) => {
        if (record.editing) {
          return (
            <Form.Item name="personCount" className="no-margin" rules={[{ required: true, message: '请输入人数' }]}>
              <InputNumber min={1} placeholder="人数" style={{ width: '100%' }} />
            </Form.Item>
          )
        }
        return text || '-'
      },
    },
    {
      title: '费用单价',
      dataIndex: 'quotedPrice',
      key: 'quotedPrice',
      width: 120,
      align: 'center' as const,
      render: (text: number, record: IBudgetItemWithKey) => {
        if (record.editing) {
          return (
            <Form.Item name="quotedPrice" className="no-margin">
              <InputNumber
                min={0}
                precision={2}
                placeholder="单价"
                style={{ width: '100%' }}
                prefix={project?.currencySymbol || '¥'}
              />
            </Form.Item>
          )
        }
        return text ? `${project?.currencySymbol || '¥'}${text.toLocaleString()}` : '-'
      },
    },
    {
      title: '拍摄天数（用工周期）',
      dataIndex: 'dayCount',
      key: 'dayCount',
      width: 150,
      align: 'center' as const,
      render: (text: number, record: IBudgetItemWithKey) => {
        if (record.editing) {
          return (
            <Form.Item name="dayCount" className="no-margin" rules={[{ required: true, message: '请输入天数' }]}>
              <InputNumber min={1} placeholder="天数" style={{ width: '100%' }} />
            </Form.Item>
          )
        }
        return text || '-'
      },
    },
    {
      title: '费用金额（元）',
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      width: 150,
      align: 'center' as const,
      render: (text: number, record: IBudgetItemWithKey) => {
        if (record.editing) {
          return (
            <Form.Item name="totalPrice" className="no-margin">
              <InputNumber
                min={0}
                precision={2}
                placeholder="总金额"
                style={{ width: '100%' }}
                prefix={project?.currencySymbol || '¥'}
                readOnly
              />
            </Form.Item>
          )
        }
        return text ? `${project?.currencySymbol || '¥'}${text.toLocaleString()}` : '-'
      },
    },
    {
      title: '有无合同',
      dataIndex: 'hasInvoice',
      key: 'hasInvoice',
      width: 100,
      align: 'center' as const,
      render: (text: number, record: IBudgetItemWithKey) => {
        if (record.editing) {
          return (
            <Form.Item name="hasInvoice" className="no-margin">
              <Select placeholder="选择" options={HAS_INVOICE_OPTIONS} style={{ width: '100%' }} />
            </Form.Item>
          )
        }
        return HAS_INVOICE_OPTIONS.find(option => option.value === text)?.label || '-'
      },
    },
    {
      title: '备注',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      render: (text: string, record: IBudgetItemWithKey) => {
        if (record.editing) {
          return (
            <Form.Item name="description" className="no-margin">
              <Input.TextArea rows={1} placeholder="备注" />
            </Form.Item>
          )
        }
        return text || '-'
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      align: 'center' as const,
      render: (_, record: IBudgetItemWithKey) => {
        if (record.editing) {
          return (
            <Space size="small">
              <Button
                type="link"
                size="small"
                icon={<SaveOutlined />}
                onClick={() => handleSaveRow(record.key)}>
                保存
              </Button>
              <Button type="link" size="small" onClick={() => handleCancelEdit(record.key)}>
                取消
              </Button>
            </Space>
          )
        }

        return (
          <Space size="small">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditRow(record.key)}>
              编辑
            </Button>
            <Popconfirm
              title="确定删除这条记录吗？"
              onConfirm={() => handleDeleteRow(record.key)}
              okText="确定"
              cancelText="取消">
              <Button type="link" size="small" danger icon={<DeleteOutlined />}>
                删除
              </Button>
            </Popconfirm>
          </Space>
        )
      },
    },
  ]

  return (
    <div>
      {/* 顶部工具栏 */}
      <Flex justify="space-between" align="center" style={{ marginBottom: 16 }}>
        <Title level={4} className="no-margin">
          新预算
        </Title>
        <Space size={12}>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAddRow}>
            添加项目
          </Button>
          <Button
            type="primary"
            ghost
            icon={<SaveOutlined />}
            loading={saving}
            onClick={handleSaveAll}
            disabled={budgetItems.some(item => item.editing)}>
            批量保存
          </Button>
        </Space>
      </Flex>

      {/* 预算表格 */}
      <Card size="small">
        <Form form={form} component={false}>
          <Table
            columns={columns}
            dataSource={budgetItems}
            loading={loading}
            pagination={false}
            size="small"
            bordered
            scroll={{ x: 1200 }}
            rowKey="key"
          />
        </Form>
      </Card>
    </div>
  )
}

export default NewBudgetTab
